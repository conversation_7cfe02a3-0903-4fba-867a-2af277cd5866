@import 'tailwindcss';

:root {
  --background: #1a1a1a;
  --foreground: #f5f5f5;
  --primary: #32cd32;
  --primary-dark: #228b22;
  --primary-light: #90ee90;
  --secondary: #2f2f2f;
  --muted: #9ca3af;
  --border: #374151;
  --input: #2d2d2d;
  --card: #262626;
  --destructive: #ef4444;
  --nav-bg: #2f2f2f;
  --section-bg: #1f1f1f;
  --hero-bg: #242424;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-primary: var(--primary);
  --color-primary-dark: var(--primary-dark);
  --color-primary-light: var(--primary-light);
  --color-secondary: var(--secondary);
  --color-muted: var(--muted);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-card: var(--card);
  --color-destructive: var(--destructive);
  --color-nav-bg: var(--nav-bg);
  --color-section-bg: var(--section-bg);
  --color-hero-bg: var(--hero-bg);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0f0f0f;
    --foreground: #f0f0f0;
    --secondary: #2f2f2f;
    --muted: #a1a1aa;
    --border: #404040;
    --input: #1a1a1a;
    --card: #1a1a1a;
    --nav-bg: #1f1f1f;
    --section-bg: #141414;
    --hero-bg: #1a1a1a;
  }
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: var(--font-sans), Arial, Helvetica, sans-serif;
}

/* Custom animations and transitions */
.animate-fade-in {
  animation: fadeIn 0.6s ease-in-out;
}

.animate-slide-up {
  animation: slideUp 0.8s ease-out;
}

.animate-scale-in {
  animation: scaleIn 0.5s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* Enhanced hover effects */
.hover-lift {
  transition:
    transform 0.3s ease,
    box-shadow 0.3s ease;
}

.hover-lift:hover {
  transform: translateY(-4px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
}

.hover-glow {
  transition: box-shadow 0.3s ease;
}

.hover-glow:hover {
  box-shadow: 0 0 20px rgba(50, 205, 50, 0.3);
}

/* Smooth scrolling */
html {
  scroll-behavior: smooth;
}
